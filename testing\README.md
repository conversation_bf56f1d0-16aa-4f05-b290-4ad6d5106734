# ContiNew Admin 100%功能覆盖黑盒测试指南

## 🎯 测试目标
实现ContiNew Admin群组记账系统的100%功能覆盖黑盒测试，确保所有API接口、机器人功能、第三方集成等功能正常工作。

## 📁 测试文件结构
```
testing/
├── README.md                           # 本文档
├── postman/                           # Postman测试集合
│   ├── ContiNew-Admin-Environment.json # 环境配置
│   ├── 01-Group-Management-API-Tests.json # 群组管理API测试
│   ├── 02-Transaction-Management-API-Tests.json # 交易管理API测试
│   ├── 03-Wallet-Management-API-Tests.json # 钱包管理API测试
│   ├── 04-Debt-Management-API-Tests.json # 债务管理API测试
│   └── 99-Cleanup-Tests.json          # 清理测试数据
├── bot-tests/                         # 机器人测试
│   ├── telegram-test-scenarios.md     # Telegram测试场景
│   ├── discord-test-scenarios.md      # Discord测试场景
│   └── bot-test-scripts/              # 自动化测试脚本
├── integration-tests/                 # 第三方集成测试
│   ├── ocr-test-images/               # OCR测试图片
│   ├── google-sheets-tests/           # Google Sheets测试
│   └── zapier-tests/                  # Zapier集成测试
└── reports/                           # 测试报告
    ├── api-test-results/              # API测试结果
    ├── bot-test-results/              # 机器人测试结果
    └── coverage-reports/              # 覆盖率报告
```

## 🚀 快速开始

### 第一步：环境准备

1. **启动ContiNew Admin服务**
   ```bash
   cd continew-admin
   mvn clean compile
   mvn spring-boot:run -pl continew-server
   ```

2. **验证服务启动**
   - 访问: http://localhost:8000
   - API文档: http://localhost:8000/doc.html

3. **导入Postman环境和集合**
   - 导入 `ContiNew-Admin-Environment.json`
   - 导入所有测试集合文件

### 第二步：执行API黑盒测试

#### 1. 群组管理API测试 (当前任务)

**测试覆盖范围:**
- ✅ 用户认证登录
- ✅ 群组CRUD操作 (创建、查询、更新、删除)
- ✅ 成员管理 (添加、移除、角色更新)
- ✅ 邀请码系统 (生成、使用、验证)
- ✅ 群组统计和设置
- ✅ 权限检查

**执行步骤:**
1. 在Postman中选择 "ContiNew Admin - Development" 环境
2. 运行 "01-群组管理API黑盒测试" 集合
3. 检查所有测试是否通过
4. 查看测试报告

**预期结果:**
- 所有API调用返回200状态码
- 响应数据格式正确
- 业务逻辑验证通过
- 环境变量正确设置

#### 2. 交易管理API测试 (下一步)

**测试场景:**
- 创建各种类型的交易记录
- 分摊计算验证
- 批量操作测试
- 统计分析功能
- 数据导入导出

#### 3. 钱包管理API测试

**测试场景:**
- 多币种钱包创建
- 余额管理和转账
- 汇率转换验证
- 历史记录查询

#### 4. 债务管理API测试

**测试场景:**
- 债务记录创建
- 分期还款处理
- 自动结算计算
- 债务统计分析

## 🤖 机器人功能测试

### Telegram Bot测试

**测试环境准备:**
1. 创建测试Telegram群组
2. 邀请机器人加入群组
3. 准备测试用户账号

**测试场景:**
```
场景1: 基础记账命令
用户输入: "-50 午餐"
期望: 创建50元支出记录

场景2: 分摊记账
用户输入: "-200 聚餐 @张三 @李四"
期望: 创建200元支出，分摊给指定用户

场景3: 查询命令
用户输入: "查看本月支出"
期望: 返回当月支出统计
```

### Discord Bot测试

**测试场景:**
- Slash Commands功能
- 交互式界面测试
- 权限验证

## 🔧 专项功能测试

### OCR收据识别测试

**测试素材准备:**
- 收集各种真实收据图片
- 不同格式、清晰度的图片
- 中英文收据

**测试步骤:**
1. 上传收据图片
2. 验证OCR识别结果
3. 检查数据提取准确性

### Google Sheets集成测试

**测试步骤:**
1. 创建测试Google Sheets
2. 配置同步设置
3. 测试双向数据同步
4. 验证冲突处理机制

### Zapier集成测试

**测试步骤:**
1. 配置Zapier工作流
2. 测试Webhook触发
3. 验证数据传输
4. 检查错误处理

## 📊 测试覆盖率要求

### API接口覆盖率: 100%
- 所有REST API端点
- 所有HTTP方法 (GET, POST, PUT, DELETE)
- 所有参数组合
- 错误场景测试

### 业务功能覆盖率: 100%
- 所有用户操作流程
- 所有业务规则验证
- 所有数据计算逻辑
- 所有权限控制

### 异常场景覆盖率: 90%
- 参数验证错误
- 权限不足错误
- 数据不存在错误
- 网络异常处理

## 🎯 当前进度

### ✅ 已完成
- [x] 测试环境配置
- [x] 群组管理API测试集合创建
- [x] 测试执行指南编写

### 🔄 进行中
- [ ] 群组管理API测试执行
- [ ] 测试结果验证

### 📋 待完成
- [ ] 交易管理API测试集合
- [ ] 钱包管理API测试集合
- [ ] 债务管理API测试集合
- [ ] 机器人功能测试
- [ ] 第三方集成测试
- [ ] 性能测试
- [ ] 安全测试

## 🚨 注意事项

1. **测试数据隔离**: 使用独立的测试数据库
2. **环境一致性**: 确保测试环境与生产环境配置一致
3. **测试顺序**: 按照依赖关系执行测试
4. **数据清理**: 每次测试后清理测试数据
5. **错误记录**: 详细记录所有测试失败情况

## 📞 支持

如果在测试过程中遇到问题，请：
1. 检查服务是否正常启动
2. 验证环境变量配置
3. 查看API文档确认接口规范
4. 检查测试数据是否正确

---

**下一步**: 让我们开始执行群组管理API测试！
