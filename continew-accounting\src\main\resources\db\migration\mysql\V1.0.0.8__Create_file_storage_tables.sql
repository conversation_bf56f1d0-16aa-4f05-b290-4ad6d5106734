-- 文件存储相关表结构

-- 按照外键依赖关系的正确顺序删除表
DROP TABLE IF EXISTS `acc_file_access_log`;
DROP TABLE IF EXISTS `acc_file_storage`;
DROP TABLE IF EXISTS `acc_file_storage_config`;

-- 文件存储配置表（先创建，因为可能被其他表引用）
CREATE TABLE acc_file_storage_config (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    config_code VARCHAR(50) NOT NULL COMMENT '配置代码',
    storage_type VARCHAR(50) NOT NULL COMMENT '存储类型',
    access_key_id VARCHAR(200) COMMENT '访问密钥ID',
    access_key_secret VARCHAR(500) COMMENT '访问密钥Secret',
    endpoint VARCHAR(200) COMMENT '端点地址',
    bucket_name VARCHAR(100) COMMENT '存储桶名称',
    region VARCHAR(50) COMMENT '存储区域',
    custom_domain VARCHAR(200) COMMENT '自定义域名',
    cdn_domain VARCHAR(200) COMMENT 'CDN域名',
    enable_https TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用HTTPS',
    enable_cdn TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用CDN',
    is_default TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为默认配置',
    max_file_size BIGINT COMMENT '最大文件大小（字节）',
    allowed_file_types VARCHAR(500) COMMENT '允许的文件类型',
    forbidden_file_types VARCHAR(500) COMMENT '禁止的文件类型',
    path_prefix VARCHAR(100) COMMENT '上传路径前缀',
    enable_thumbnail TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用缩略图',
    thumbnail_config TEXT COMMENT '缩略图配置',
    enable_watermark TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用水印',
    watermark_config TEXT COMMENT '水印配置',
    enable_security_scan TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用安全扫描',
    security_scan_config TEXT COMMENT '安全扫描配置',
    status VARCHAR(20) NOT NULL DEFAULT 'ENABLE' COMMENT '配置状态',
    sort INT NOT NULL DEFAULT 0 COMMENT '排序',
    description VARCHAR(500) COMMENT '配置描述',
    extend_config TEXT COMMENT '扩展配置（JSON格式）',
    group_id BIGINT NOT NULL COMMENT '群组ID',
    create_user_id BIGINT COMMENT '创建人ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user_id BIGINT COMMENT '修改人ID',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_config_code_group (config_code, group_id),
    INDEX idx_storage_type (storage_type),
    INDEX idx_status (status),
    INDEX idx_is_default (is_default),
    INDEX idx_group_id (group_id),
    INDEX idx_sort (sort),
    CONSTRAINT fk_file_storage_config_group FOREIGN KEY (group_id) REFERENCES acc_group(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件存储配置表';

-- 文件存储表
CREATE TABLE acc_file_storage (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名称',
    original_file_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_url VARCHAR(500) COMMENT '文件URL',
    file_size BIGINT NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
    file_type VARCHAR(50) COMMENT '文件类型',
    file_extension VARCHAR(20) COMMENT '文件扩展名',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_md5 VARCHAR(32) COMMENT '文件MD5值',
    file_sha256 VARCHAR(64) COMMENT '文件SHA256值',
    storage_type VARCHAR(50) NOT NULL COMMENT '存储类型',
    bucket_name VARCHAR(100) COMMENT '存储桶名称',
    storage_region VARCHAR(50) COMMENT '存储区域',
    access_type VARCHAR(20) NOT NULL DEFAULT 'PRIVATE' COMMENT '访问权限类型',
    cdn_url VARCHAR(500) COMMENT 'CDN URL',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    preview_url VARCHAR(500) COMMENT '预览URL',
    download_count BIGINT NOT NULL DEFAULT 0 COMMENT '下载次数',
    access_count BIGINT NOT NULL DEFAULT 0 COMMENT '访问次数',
    last_access_time DATETIME COMMENT '最后访问时间',
    process_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '处理状态',
    process_result TEXT COMMENT '处理结果',
    file_tags VARCHAR(200) COMMENT '文件标签',
    file_description VARCHAR(500) COMMENT '文件描述',
    is_temporary TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为临时文件',
    expire_time DATETIME COMMENT '过期时间',
    business_id VARCHAR(100) COMMENT '关联业务ID',
    business_type VARCHAR(50) COMMENT '关联业务类型',
    group_id BIGINT NOT NULL COMMENT '群组ID',
    upload_user_id BIGINT NOT NULL COMMENT '上传用户ID',
    metadata TEXT COMMENT '文件元数据（JSON格式）',
    security_scan_result TEXT COMMENT '安全扫描结果',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已删除',
    delete_time DATETIME COMMENT '删除时间',
    create_user_id BIGINT COMMENT '创建人ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user_id BIGINT COMMENT '修改人ID',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (id),
    INDEX idx_file_md5 (file_md5),
    INDEX idx_file_sha256 (file_sha256),
    INDEX idx_storage_type (storage_type),
    INDEX idx_access_type (access_type),
    INDEX idx_process_status (process_status),
    INDEX idx_business (business_type, business_id),
    INDEX idx_group_id (group_id),
    INDEX idx_upload_user_id (upload_user_id),
    INDEX idx_is_temporary (is_temporary),
    INDEX idx_expire_time (expire_time),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_create_time (create_time),
    INDEX idx_file_type (file_type),
    INDEX idx_file_size (file_size),
    INDEX idx_download_count (download_count),
    INDEX idx_access_count (access_count),
    CONSTRAINT fk_file_storage_group FOREIGN KEY (group_id) REFERENCES acc_group(id) ON DELETE CASCADE,
    CONSTRAINT fk_file_storage_upload_user FOREIGN KEY (upload_user_id) REFERENCES sys_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件存储表';

-- 文件访问日志表
CREATE TABLE acc_file_access_log (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    file_id BIGINT NOT NULL COMMENT '文件ID',
    access_type VARCHAR(20) NOT NULL COMMENT '访问类型',
    user_agent VARCHAR(500) COMMENT '用户代理',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    user_id BIGINT COMMENT '用户ID',
    access_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
    PRIMARY KEY (id),
    INDEX idx_file_id (file_id),
    INDEX idx_access_type (access_type),
    INDEX idx_user_id (user_id),
    INDEX idx_access_time (access_time),
    INDEX idx_client_ip (client_ip),
    CONSTRAINT fk_file_access_log_file FOREIGN KEY (file_id) REFERENCES acc_file_storage(id) ON DELETE CASCADE,
    CONSTRAINT fk_file_access_log_user FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件访问日志表';

-- 注意：初始数据插入需要在 acc_group 表有数据后执行
-- 可以通过以下SQL手动插入默认配置（将 {group_id} 替换为实际的群组ID）：
/*
INSERT INTO acc_file_storage_config (
    config_name, config_code, storage_type, is_default, status, sort, description, group_id,
    create_user_id, create_time, update_user_id, update_time
) VALUES
('本地存储', 'local', 'LOCAL', 1, 'ENABLE', 1, '本地文件系统存储', {group_id}, 1, NOW(), 1, NOW()),
('阿里云OSS', 'aliyun-oss', 'ALIYUN_OSS', 0, 'DISABLE', 2, '阿里云对象存储服务', {group_id}, 1, NOW(), 1, NOW()),
('腾讯云COS', 'tencent-cos', 'TENCENT_COS', 0, 'DISABLE', 3, '腾讯云对象存储服务', {group_id}, 1, NOW(), 1, NOW()),
('AWS S3', 'aws-s3', 'AWS_S3', 0, 'DISABLE', 4, 'Amazon S3对象存储服务', {group_id}, 1, NOW(), 1, NOW()),
('MinIO', 'minio', 'MINIO', 0, 'DISABLE', 5, 'MinIO对象存储服务', {group_id}, 1, NOW(), 1, NOW());
*/
