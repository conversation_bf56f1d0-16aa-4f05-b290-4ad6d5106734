{"info": {"name": "01-群组管理API黑盒测试", "description": "群组管理完整生命周期测试：创建→查询→更新→成员管理→邀请码→统计→删除", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "01-认证登录", "item": [{"name": "获取验证码", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/captcha/image", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON>a", "image"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('验证码获取成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql('0');", "    if (response.data && response.data.uuid) {", "        pm.environment.set('captcha_uuid', response.data.uuid);", "        console.log('验证码UUID已保存:', response.data.uuid);", "        console.log('请手动查看验证码图片并在下一个请求中输入验证码');", "    } else {", "        console.log('验证码已禁用，无需输入');", "    }", "});"]}}]}, {"name": "用户登录获取Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"authType\": \"ACCOUNT\",\n  \"clientId\": \"{{client_id}}\",\n  \"username\": \"{{username}}\",\n  \"password\": \"{{password}}\",\n  \"captcha\": \"{{captcha_code}}\",\n  \"uuid\": \"{{captcha_uuid}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["// 验证响应状态", "pm.test('登录成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.token).to.not.be.empty;", "});", "", "// 保存Token到环境变量", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.data.token);", "    pm.environment.set('tenant_id', response.data.tenantId || '');", "    console.log('Token已保存:', response.data.token);", "}"]}}]}]}, {"name": "02-群组CRUD操作", "item": [{"name": "创建测试群组", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"家庭开支群\",\n  \"description\": \"用于记录家庭日常开支\",\n  \"avatar\": \"https://example.com/avatar.jpg\",\n  \"defaultCurrency\": \"CNY\",\n  \"isPublic\": false\n}"}, "url": {"raw": "{{base_url}}{{api_prefix}}/groups", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('群组创建成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data).to.be.a('number');", "    pm.expect(response.data).to.be.greaterThan(0);", "});", "", "// 保存群组ID", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('test_group_id', response.data);", "    console.log('群组ID已保存:', response.data);", "}"]}}]}, {"name": "查询群组详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups/{{test_group_id}}", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "{{test_group_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('群组详情查询成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.name).to.eql('家庭开支群');", "    pm.expect(response.data.defaultCurrency).to.eql('CNY');", "    pm.expect(response.data.isPublic).to.eql(false);", "});"]}}]}, {"name": "更新群组信息", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"家庭开支群-已更新\",\n  \"description\": \"更新后的群组描述\",\n  \"avatar\": \"https://example.com/new-avatar.jpg\",\n  \"defaultCurrency\": \"CNY\",\n  \"isPublic\": false\n}"}, "url": {"raw": "{{base_url}}{{api_prefix}}/groups/{{test_group_id}}", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "{{test_group_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('群组更新成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}]}, {"name": "分页查询群组列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups?page=1&size=10&name=家庭", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "name", "value": "家庭"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('群组列表查询成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.list).to.be.an('array');", "    pm.expect(response.data.total).to.be.a('number');", "});"]}}]}]}, {"name": "03-成员管理", "item": [{"name": "获取群组成员列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups/{{test_group_id}}/members", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "{{test_group_id}}", "members"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('成员列表查询成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data).to.be.an('array');", "});"]}}]}, {"name": "添加群组成员", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups/{{test_group_id}}/members?userId=2&role=MEMBER", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "{{test_group_id}}", "members"], "query": [{"key": "userId", "value": "2"}, {"key": "role", "value": "MEMBER"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('成员添加成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}]}]}, {"name": "04-邀请码管理", "item": [{"name": "生成邀请码", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups/{{test_group_id}}/invite-code?expireHours=24", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "{{test_group_id}}", "invite-code"], "query": [{"key": "expireHours", "value": "24"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('邀请码生成成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data).to.be.a('string');", "    pm.expect(response.data.length).to.be.greaterThan(0);", "});", "", "// 保存邀请码", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('invite_code', response.data);", "    console.log('邀请码已保存:', response.data);", "}"]}}]}, {"name": "使用邀请码加入群组", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups/{{test_group_id}}/join?inviteCode={{invite_code}}", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "{{test_group_id}}", "join"], "query": [{"key": "inviteCode", "value": "{{invite_code}}"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('邀请码加入成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}]}]}, {"name": "05-群组统计和设置", "item": [{"name": "获取群组统计信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups/{{test_group_id}}/statistics", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "{{test_group_id}}", "statistics"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('群组统计查询成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data).to.be.an('object');", "});"]}}]}, {"name": "获取用户群组列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups/my", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "my"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('用户群组列表查询成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data).to.be.an('array');", "});"]}}]}, {"name": "检查群组权限", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}{{api_prefix}}/groups/{{test_group_id}}/permissions", "host": ["{{base_url}}"], "path": ["{{api_prefix}}", "groups", "{{test_group_id}}", "permissions"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('权限检查成功', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data).to.be.an('array');", "});"]}}]}]}]}