# ContiNew Admin API 快速测试脚本
# 用于验证API是否正常工作

$baseUrl = "http://localhost:8000"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "🚀 开始ContiNew Admin API测试..." -ForegroundColor Green

# 1. 测试服务状态
Write-Host "`n1️⃣ 检查服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl" -Method GET -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 服务正常运行" -ForegroundColor Green
        $data = $response.Content | ConvertFrom-Json
        Write-Host "   应用名称: $($data.data.name)" -ForegroundColor Cyan
        Write-Host "   版本: $($data.data.version)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ 服务连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试登录
Write-Host "`n2️⃣ 测试用户登录..." -ForegroundColor Yellow
$loginData = '{"authType":"ACCOUNT","clientId":"web","username":"admin","password":"admin123"}'

try {
    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/login" -Method POST -Body $loginData -Headers $headers -UseBasicParsing
    if ($loginResponse.StatusCode -eq 200) {
        $loginResult = $loginResponse.Content | ConvertFrom-Json
        if ($loginResult.code -eq 200) {
            Write-Host "✅ 登录成功" -ForegroundColor Green
            $token = $loginResult.data.token
            Write-Host "   Token: $($token.Substring(0, 20))..." -ForegroundColor Cyan
            
            # 更新请求头，添加认证信息
            $authHeaders = $headers.Clone()
            $authHeaders["Authorization"] = "Bearer $token"
        } else {
            Write-Host "❌ 登录失败: $($loginResult.msg)" -ForegroundColor Red
            exit 1
        }
    }
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. 测试群组API
Write-Host "`n3️⃣ 测试群组管理API..." -ForegroundColor Yellow

# 3.1 创建群组
Write-Host "   📝 创建测试群组..." -ForegroundColor Cyan
$groupData = '{"name":"API测试群组","description":"用于API测试的群组","defaultCurrency":"CNY","isPublic":false}'

try {
    $createResponse = Invoke-WebRequest -Uri "$baseUrl/api/v1/groups" -Method POST -Body $groupData -Headers $authHeaders -UseBasicParsing
    if ($createResponse.StatusCode -eq 200) {
        $createResult = $createResponse.Content | ConvertFrom-Json
        if ($createResult.code -eq 200) {
            Write-Host "   ✅ 群组创建成功" -ForegroundColor Green
            $groupId = $createResult.data
            Write-Host "      群组ID: $groupId" -ForegroundColor Cyan
        } else {
            Write-Host "   ❌ 群组创建失败: $($createResult.msg)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "   ❌ 群组创建请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3.2 查询群组详情
if ($groupId) {
    Write-Host "   🔍 查询群组详情..." -ForegroundColor Cyan
    try {
        $getResponse = Invoke-WebRequest -Uri "$baseUrl/api/v1/groups/$groupId" -Method GET -Headers $authHeaders -UseBasicParsing
        if ($getResponse.StatusCode -eq 200) {
            $getResult = $getResponse.Content | ConvertFrom-Json
            if ($getResult.code -eq 200) {
                Write-Host "   ✅ 群组查询成功" -ForegroundColor Green
                Write-Host "      群组名称: $($getResult.data.name)" -ForegroundColor Cyan
                Write-Host "      默认货币: $($getResult.data.defaultCurrency)" -ForegroundColor Cyan
                Write-Host "      成员数量: $($getResult.data.memberCount)" -ForegroundColor Cyan
            } else {
                Write-Host "   ❌ 群组查询失败: $($getResult.msg)" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "   ❌ 群组查询请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 3.3 查询群组列表
Write-Host "   📋 查询群组列表..." -ForegroundColor Cyan
try {
    $listResponse = Invoke-WebRequest -Uri "$baseUrl/api/v1/groups?page=1`&size=10" -Method GET -Headers $authHeaders -UseBasicParsing
    if ($listResponse.StatusCode -eq 200) {
        $listResult = $listResponse.Content | ConvertFrom-Json
        if ($listResult.code -eq 200) {
            Write-Host "   ✅ 群组列表查询成功" -ForegroundColor Green
            Write-Host "      总数: $($listResult.data.total)" -ForegroundColor Cyan
            Write-Host "      当前页数据: $($listResult.data.list.Count)" -ForegroundColor Cyan
        } else {
            Write-Host "   ❌ 群组列表查询失败: $($listResult.msg)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "   ❌ 群组列表查询请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试完成
Write-Host "`n🎉 API测试完成！" -ForegroundColor Green
Write-Host "📊 测试结果总结:" -ForegroundColor Yellow
Write-Host "   ✅ 服务状态检查" -ForegroundColor Green
Write-Host "   ✅ 用户认证登录" -ForegroundColor Green
Write-Host "   ✅ 群组创建功能" -ForegroundColor Green
Write-Host "   ✅ 群组查询功能" -ForegroundColor Green
Write-Host "   ✅ 群组列表功能" -ForegroundColor Green

Write-Host "`n🔗 下一步操作:" -ForegroundColor Yellow
Write-Host "   1. 在Postman中导入测试集合" -ForegroundColor Cyan
Write-Host "   2. 运行完整的群组管理API测试" -ForegroundColor Cyan
Write-Host "   3. 检查所有测试用例是否通过" -ForegroundColor Cyan
Write-Host "   4. 继续其他模块的API测试" -ForegroundColor Cyan

Write-Host "`n📁 测试文件位置:" -ForegroundColor Yellow
Write-Host "   环境配置: testing/postman/ContiNew-Admin-Environment.json" -ForegroundColor Cyan
Write-Host "   测试集合: testing/postman/01-Group-Management-API-Tests.json" -ForegroundColor Cyan
